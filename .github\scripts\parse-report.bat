@echo off
setlocal enabledelayedexpansion

REM Windows batch script for parsing Cypress Cucumber JSON reports
REM Adapted for Veritone Illuminate App

set "REPORT_FILE=cypress\reports\cucumber-json.json"
set "SLACK_MESSAGE="
set "MAX_INLINE_FAILURES=3"
set "OUTPUT_FILE=report_output.txt"

echo Parsing Cypress E2E Test Report...

if exist "%REPORT_FILE%" (
    echo Found report file: %REPORT_FILE%
    
    REM Check if jq is available
    where jq >nul 2>&1
    if errorlevel 1 (
        echo ERROR: jq is not installed or not in PATH
        echo Please install jq from https://stedolan.github.io/jq/download/
        echo Or use chocolatey: choco install jq
        pause
        exit /b 1
    )
    
    REM Generate summary using JQ script
    echo Generating test summary...
    jq -r -f .github\scripts\e2e-report-handler.jq "%REPORT_FILE%" > temp_summary.txt
    
    if errorlevel 1 (
        echo ERROR: Failed to process report with jq
        pause
        exit /b 1
    )
    
    REM Calculate totals using jq (simplified approach)
    echo Calculating test totals...

    REM Create temporary jq script file to avoid command line issues
    echo .[] ^| select(type == "object") ^| { total: (.elements ^| length), passed: ([.elements[]? ^| select((.steps ^| all(.result.status == "passed")))] ^| length), failed: ([.elements[]? ^| select((.steps[]?.result.status == "failed"))] ^| length), skipped: ([.elements[]? ^| select((.steps ^| all(.result.status == "skipped")))] ^| length) } ^| [.total, .passed, .failed, .skipped] ^| @tsv > temp_totals.jq

    REM Run jq with the script file
    jq -r -f temp_totals.jq "%REPORT_FILE%" > temp_totals.txt

    REM Parse the results
    set /a "TOTAL_TESTS=0"
    set /a "PASSED_TESTS=0"
    set /a "FAILED_TESTS=0"
    set /a "SKIPPED_TESTS=0"

    for /f "tokens=1,2,3,4" %%a in (temp_totals.txt) do (
        set /a "TOTAL_TESTS+=%%a"
        set /a "PASSED_TESTS+=%%b"
        set /a "FAILED_TESTS+=%%c"
        set /a "SKIPPED_TESTS+=%%d"
    )
    
    REM Get failed scenarios details (simplified)
    echo Extracting failed scenarios...

    REM Create jq script for failed scenarios
    echo .[] as $feature ^| $feature.elements[]? ^| select((.steps ^| any(.result.status == "failed")) or (.steps == null)) ^| "- Feature: " + (($feature.uri // "" ^| gsub("\\\\"; "/") ^| split("/") ^| last ^| sub(".feature$"; ""))) + "\n  Scenario: " + .name > temp_failed.jq

    jq -r -f temp_failed.jq "%REPORT_FILE%" > failed_scenarios.txt 2>nul
    
    REM Determine test status
    if !FAILED_TESTS! gtr 0 (
        set "TEST_STATUS=FAILURE"
        set "STATUS_EMOJI=❌"
    ) else (
        set "TEST_STATUS=SUCCESS"
        set "STATUS_EMOJI=✅"
    )
    
    REM Create formatted report
    echo.
    echo ============================================
    echo   VERITONE ILLUMINATE APP - E2E TEST REPORT
    echo ============================================
    echo.
    echo Status: !STATUS_EMOJI! !TEST_STATUS!
    echo.
    echo Test Summary:
    echo   Total Tests:   !TOTAL_TESTS!
    echo   Passed:        !PASSED_TESTS!
    echo   Failed:        !FAILED_TESTS!
    echo   Skipped:       !SKIPPED_TESTS!
    echo.
    
    REM Display feature summary table
    echo Feature Summary:
    echo ----------------------------------------
    type temp_summary.txt
    echo ----------------------------------------
    echo.
    
    REM Display failed scenarios if any
    if !FAILED_TESTS! gtr 0 (
        echo Failed Scenarios:
        if !FAILED_TESTS! leq !MAX_INLINE_FAILURES! (
            type failed_scenarios.txt 2>nul
        ) else (
            echo Too many failed scenarios to display. Check the full report.
        )
        echo.
    ) else (
        echo All scenarios passed or were skipped! 🎉
        echo.
    )
    
    REM Save detailed report to file
    (
        echo VERITONE ILLUMINATE APP - E2E TEST REPORT
        echo Generated on: %date% %time%
        echo ==========================================
        echo.
        echo Status: !TEST_STATUS!
        echo.
        echo Test Summary:
        echo   Total Tests:   !TOTAL_TESTS!
        echo   Passed:        !PASSED_TESTS!
        echo   Failed:        !FAILED_TESTS!
        echo   Skipped:       !SKIPPED_TESTS!
        echo.
        echo Feature Summary:
        type temp_summary.txt
        echo.
        if !FAILED_TESTS! gtr 0 (
            echo Failed Scenarios:
            type failed_scenarios.txt 2>nul
        )
    ) > "%OUTPUT_FILE%"
    
    echo Report saved to: %OUTPUT_FILE%
    
    REM Cleanup temporary files
    del temp_summary.txt 2>nul
    del failed_scenarios.txt 2>nul
    del temp_totals.jq 2>nul
    del temp_totals.txt 2>nul
    del temp_failed.jq 2>nul
    
) else (
    echo ERROR: Report file not found: %REPORT_FILE%
    echo Make sure Cypress tests have been run and generated the JSON report.
    echo.
    echo Expected report location: %REPORT_FILE%
    pause
    exit /b 1
)

echo.
echo Report parsing completed!
if "%TEST_STATUS%"=="FAILURE" (
    echo Some tests failed. Check the details above.
    exit /b 1
) else (
    echo All tests passed successfully!
    exit /b 0
)
