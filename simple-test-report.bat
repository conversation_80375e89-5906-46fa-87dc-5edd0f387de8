@echo off
setlocal enabledelayedexpansion

echo.
echo ============================================
echo   SIMPLE E2E REPORT TEST
echo ============================================
echo.

set "REPORT_FILE=cypress\reports\cucumber-json.json"

REM Test 1: Success scenario
echo Testing SUCCESS scenario...
copy "cypress\reports\mock-success-report.json" "%REPORT_FILE%" >nul

if exist "%REPORT_FILE%" (
    echo ✅ Report file exists
    
    echo Generating summary...
    jq -r -f .github\scripts\e2e-report-handler.jq "%REPORT_FILE%" > temp_summary.txt
    
    if exist temp_summary.txt (
        echo ✅ Summary generated successfully
        echo.
        echo --- SUCCESS REPORT ---
        type temp_summary.txt
        echo --- END REPORT ---
        echo.
    ) else (
        echo ❌ Failed to generate summary
    )
) else (
    echo ❌ Report file not found
)

echo.
echo Testing MIXED RESULTS scenario...
copy "cypress\reports\mock-mixed-report.json" "%REPORT_FILE%" >nul

if exist "%REPORT_FILE%" (
    echo ✅ Report file exists
    
    echo Generating summary...
    jq -r -f .github\scripts\e2e-report-handler.jq "%REPORT_FILE%" > temp_summary2.txt
    
    if exist temp_summary2.txt (
        echo ✅ Summary generated successfully
        echo.
        echo --- MIXED RESULTS REPORT ---
        type temp_summary2.txt
        echo --- END REPORT ---
        echo.
    ) else (
        echo ❌ Failed to generate summary
    )
) else (
    echo ❌ Report file not found
)

REM Cleanup
del temp_summary.txt 2>nul
del temp_summary2.txt 2>nul
del "%REPORT_FILE%" 2>nul

echo.
echo ============================================
echo   TEST COMPLETED
echo ============================================
pause
