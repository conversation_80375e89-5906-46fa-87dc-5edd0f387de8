@echo off
setlocal enabledelayedexpansion

echo.
echo ============================================
echo   VALIDATING E2E REPORTING SYSTEM
echo ============================================
echo.

REM Check prerequisites
echo Checking prerequisites...

REM Check Node.js
where node >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not found
    set "PREREQ_FAILED=true"
) else (
    echo ✅ Node.js found
)

REM Check Yarn
where yarn >nul 2>&1
if errorlevel 1 (
    echo ❌ Yarn not found
    set "PREREQ_FAILED=true"
) else (
    echo ✅ Yarn found
)

REM Check jq
where jq >nul 2>&1
if errorlevel 1 (
    echo ❌ jq not found (needed for report parsing)
    set "PREREQ_FAILED=true"
) else (
    echo ✅ jq found
)

REM Check Cypress
if exist "node_modules\.bin\cypress.cmd" (
    echo ✅ Cypress installed
) else (
    echo ❌ Cypress not installed
    set "PREREQ_FAILED=true"
)

if defined PREREQ_FAILED (
    echo.
    echo ❌ Some prerequisites are missing. Please install them first.
    pause
    exit /b 1
)

echo.
echo ============================================
echo   TESTING REPORT GENERATION
echo ============================================
echo.

REM Test 1: JQ Script with Success Data
echo Test 1: Testing jq script with success data...
jq -r -f .github\scripts\e2e-report-handler.jq cypress\reports\mock-success-report.json > test1_output.txt
if errorlevel 1 (
    echo ❌ JQ script failed with success data
    set "TEST_FAILED=true"
) else (
    echo ✅ JQ script works with success data
    echo Output:
    type test1_output.txt
    echo.
)

REM Test 2: JQ Script with Mixed Data
echo Test 2: Testing jq script with mixed results data...
jq -r -f .github\scripts\e2e-report-handler.jq cypress\reports\mock-mixed-report.json > test2_output.txt
if errorlevel 1 (
    echo ❌ JQ script failed with mixed data
    set "TEST_FAILED=true"
) else (
    echo ✅ JQ script works with mixed data
    echo Output:
    type test2_output.txt
    echo.
)

REM Test 3: Cypress Configuration
echo Test 3: Testing Cypress configuration...
npx cypress verify >nul 2>&1
if errorlevel 1 (
    echo ❌ Cypress verification failed
    set "TEST_FAILED=true"
) else (
    echo ✅ Cypress is properly configured
)

REM Test 4: Check Cypress Environment
echo Test 4: Checking Cypress environment configuration...
if exist "cypress.env.json" (
    echo ✅ cypress.env.json exists
    echo Configuration preview:
    jq -r "keys[]" cypress.env.json 2>nul || echo "   - Contains configuration keys"
) else (
    echo ❌ cypress.env.json missing
    set "TEST_FAILED=true"
)

REM Test 5: Check Feature Files
echo Test 5: Checking Cypress feature files...
set "FEATURE_COUNT=0"
for %%f in (cypress\e2e\features\*.feature) do (
    set /a "FEATURE_COUNT+=1"
)
if !FEATURE_COUNT! gtr 0 (
    echo ✅ Found !FEATURE_COUNT! Cypress feature files
) else (
    echo ❌ No Cypress feature files found
    set "TEST_FAILED=true"
)

REM Test 6: Check GitHub Workflow
echo Test 6: Checking GitHub Actions workflow...
if exist ".github\workflows\daily_e2e.yml" (
    echo ✅ GitHub Actions workflow exists
) else (
    echo ❌ GitHub Actions workflow missing
    set "TEST_FAILED=true"
)

echo.
echo ============================================
echo   TESTING REPORT PARSING (WINDOWS)
echo ============================================
echo.

REM Test Windows batch report parser with mock data
echo Testing Windows report parser...
copy "cypress\reports\mock-mixed-report.json" "cypress\reports\cucumber-json.json" >nul

REM Create a simple version of the parser test
echo Parsing mock report...
jq -r -f .github\scripts\e2e-report-handler.jq cypress\reports\cucumber-json.json > parsed_report.txt

if exist parsed_report.txt (
    echo ✅ Report parsing successful
    echo.
    echo --- PARSED REPORT ---
    type parsed_report.txt
    echo --- END REPORT ---
    echo.
) else (
    echo ❌ Report parsing failed
    set "TEST_FAILED=true"
)

REM Cleanup
del test1_output.txt 2>nul
del test2_output.txt 2>nul
del parsed_report.txt 2>nul
del cypress\reports\cucumber-json.json 2>nul

echo.
echo ============================================
echo   VALIDATION SUMMARY
echo ============================================
echo.

if defined TEST_FAILED (
    echo ❌ VALIDATION FAILED
    echo Some components are not working correctly.
    echo Please check the errors above and fix them.
    echo.
    echo Next steps:
    echo 1. Install any missing prerequisites
    echo 2. Fix configuration issues
    echo 3. Re-run this validation script
) else (
    echo ✅ VALIDATION SUCCESSFUL
    echo All core components are working correctly!
    echo.
    echo The E2E reporting system is ready to use.
    echo.
    echo Next steps:
    echo 1. Configure GitHub repository secrets for CI/CD
    echo 2. Test with actual Cypress runs using run-e2e-tests.bat
    echo 3. Set up Slack webhook for notifications (optional)
    echo.
    echo Available commands:
    echo   - run-e2e-tests.bat          (Run full E2E test suite)
    echo   - test-reporting-system.bat  (Test with mock data)
    echo   - npx cypress open           (Open Cypress GUI)
    echo   - npx cypress run            (Run tests headlessly)
)

echo.
pause
