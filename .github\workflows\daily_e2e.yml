name: Daily E2E Runner

on:
  schedule:
    - cron: "0 0 * * *" # 19:00 EST daily
  workflow_dispatch:

env:
  GITHUB_ACCESS_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  run_daily_e2e_tests:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install System Dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y jq
          sudo apt-get install -y libnss3-tools
          sudo apt-get install -y wget libgtk2.0-0 libgtk-3-0 libgbm-dev libnotify-dev libgconf-2-4 libnss3 libxss1 libasound2 libxtst6 xauth xvfb
          curl -sSL https://dl.filippo.io/mkcert/latest?for=linux/amd64 -o mkcert
          sudo mv mkcert /usr/local/bin/
          sudo chmod +x /usr/local/bin/mkcert

      - name: Create and Trust SSL Certificate
        run: |
          mkcert -install
          mkcert local.veritone.com
          # Trust certificate for Chrome
          CAROOT=$(mkcert -CAROOT)
          mkdir -p $HOME/.pki/nssdb
          certutil -N --empty-password -d "sql:$HOME/.pki/nssdb"
          certutil -d "sql:$HOME/.pki/nssdb" -A -t "C,," -n "mkcert" -i "$CAROOT/rootCA.pem"

      - name: Configure Hosts
        run: |
          echo "127.0.0.1 local.veritone.com" | sudo tee -a /etc/hosts

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'yarn'

      - name: Enable Corepack and Setup Yarn
        run: |
          corepack enable
          corepack prepare yarn@4.9.2 --activate

      - name: Install Dependencies
        run: |
          yarn install --immutable

      - name: Install Server Dependencies
        run: |
          yarn install --immutable
        working-directory: server

      - name: Build Application
        run: |
          yarn build
        env:
          ENVIRONMENT: local

      - name: Start Application Server
        run: |
          echo "Starting application server..."
          yarn startssl > app_startup.log 2>&1 &
          echo $! > app.pid
          sleep 10
        env:
          ENVIRONMENT: local

      - name: Wait for Application
        run: |
          timeout 60 bash -c 'until curl -k https://local.veritone.com:8080 > /dev/null 2>&1; do sleep 2; done'
          echo "Application is ready"

      - name: Create Cypress Environment Configuration
        run: |
          echo '{
            "username": "${{ secrets.CYPRESS_USER1_USERNAME }}",
            "password": "${{ secrets.CYPRESS_USER1_PASSWORD }}",
            "users": {
              "user1": {
                "username": "${{ secrets.CYPRESS_USER1_USERNAME }}",
                "password": "${{ secrets.CYPRESS_USER1_PASSWORD }}"
              },
              "user2": {
                "username": "${{ secrets.CYPRESS_USER2_USERNAME }}",
                "password": "${{ secrets.CYPRESS_USER2_PASSWORD }}"
              }
            }
          }' > cypress.env.json

      - name: Create Reports Directory
        run: |
          mkdir -p cypress/reports

      - name: Run Cypress E2E Tests
        id: cypress
        run: |
          yarn cy:run --browser chrome --config chromeWebSecurity=false
        continue-on-error: true

      - name: Upload Cypress Screenshots on Failure
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-screenshots
          path: cypress/screenshots
          retention-days: 7

      - name: Upload Cypress Videos on Failure
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-videos
          path: cypress/videos
          retention-days: 7

      - name: Upload Test Reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-reports
          path: cypress/reports
          retention-days: 30

      - name: Parse Test Report
        id: parse_report
        if: always()
        run: |
          chmod +x .github/scripts/parse-report.sh
          .github/scripts/parse-report.sh

      - name: Send Slack Notification
        uses: rtCamp/action-slack-notify@v2
        if: always()
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_USERNAME: Veritone Illuminate E2E Bot
          SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL || 'general' }}
          SLACK_MESSAGE: |
            ${{ steps.parse_report.outputs.SLACK_MESSAGE }}
          SLACK_COLOR: ${{ steps.parse_report.outputs.SLACK_MESSAGE_STATUS == 'FAILURE' && 'danger' || 'good' }}

      - name: Cleanup
        if: always()
        run: |
          if [ -f app.pid ]; then
            kill $(cat app.pid) || true
            rm -f app.pid
          fi
          # Clean up any remaining processes
          pkill -f "webpack-dev-server" || true
          pkill -f "node.*webpack" || true
